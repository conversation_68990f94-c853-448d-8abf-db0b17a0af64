import React from 'react';
import { Card, Row, Col, Statistic, Table, DatePicker } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const Statistics = () => {
  const columns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: '订单数量',
      dataIndex: 'orderCount',
      key: 'orderCount',
    },
    {
      title: '营业额',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (amount) => `¥${amount}`,
    },
    {
      title: '客户数量',
      dataIndex: 'customerCount',
      key: 'customerCount',
    },
  ];

  const data = [
    {
      key: '1',
      date: '2024-12-01',
      orderCount: 25,
      revenue: 7500,
      customerCount: 20,
    },
    {
      key: '2',
      date: '2024-11-30',
      orderCount: 18,
      revenue: 5400,
      customerCount: 15,
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日营业额"
              value={11280}
              precision={2}
              valueStyle={{ color: '#3f8600' }}
              prefix="¥"
              suffix={<ArrowUpOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日订单"
              value={32}
              valueStyle={{ color: '#3f8600' }}
              suffix={<ArrowUpOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃客户"
              value={128}
              valueStyle={{ color: '#cf1322' }}
              suffix={<ArrowDownOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="休息室使用率"
              value={85.2}
              precision={1}
              valueStyle={{ color: '#3f8600' }}
              suffix="%"
            />
          </Card>
        </Col>
      </Row>

      <Card
        title="销售统计"
        extra={<DatePicker.RangePicker />}
      >
        <Table columns={columns} dataSource={data} />
      </Card>
    </div>
  );
};

export default Statistics;
