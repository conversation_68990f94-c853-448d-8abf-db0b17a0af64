import React from 'react';
import { Card, Form, Input, Button, Switch, Select, Space, Divider } from 'antd';
import { SaveOutlined } from '@ant-design/icons';

const { Option } = Select;

const Settings = () => {
  const [form] = Form.useForm();

  const onFinish = (values) => {
    console.log('设置保存:', values);
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="系统设置">
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            systemName: '高舱休息室销售系统',
            autoApproval: true,
            emailNotification: false,
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
          }}
        >
          <Divider orientation="left">基本设置</Divider>
          
          <Form.Item
            label="系统名称"
            name="systemName"
            rules={[{ required: true, message: '请输入系统名称' }]}
          >
            <Input placeholder="请输入系统名称" />
          </Form.Item>

          <Form.Item
            label="系统描述"
            name="systemDescription"
          >
            <Input.TextArea 
              rows={3} 
              placeholder="请输入系统描述"
            />
          </Form.Item>

          <Form.Item
            label="语言设置"
            name="language"
          >
            <Select>
              <Option value="zh-CN">简体中文</Option>
              <Option value="en-US">English</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="时区设置"
            name="timezone"
          >
            <Select>
              <Option value="Asia/Shanghai">北京时间 (UTC+8)</Option>
              <Option value="Asia/Tokyo">东京时间 (UTC+9)</Option>
              <Option value="America/New_York">纽约时间 (UTC-5)</Option>
            </Select>
          </Form.Item>

          <Divider orientation="left">业务设置</Divider>

          <Form.Item
            label="自动审批订单"
            name="autoApproval"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="邮件通知"
            name="emailNotification"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            label="默认休息室使用时长"
            name="defaultDuration"
          >
            <Select placeholder="请选择默认时长">
              <Option value="1">1小时</Option>
              <Option value="2">2小时</Option>
              <Option value="3">3小时</Option>
              <Option value="4">4小时</Option>
            </Select>
          </Form.Item>

          <Divider orientation="left">联系信息</Divider>

          <Form.Item
            label="客服电话"
            name="servicePhone"
          >
            <Input placeholder="请输入客服电话" />
          </Form.Item>

          <Form.Item
            label="客服邮箱"
            name="serviceEmail"
          >
            <Input placeholder="请输入客服邮箱" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                保存设置
              </Button>
              <Button onClick={() => form.resetFields()}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Settings;
